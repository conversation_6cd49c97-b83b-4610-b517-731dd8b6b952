<template>
  <div class="network-infrastructure-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Network class="title-icon" />
            网络基础设施监控
          </h1>
          <p class="page-description">监控IP地址池、DHCP服务、DNS解析和网络基础设施状态</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="showConfigModal"
        >
          <Settings class="btn-icon" />
          监控配置
        </a-button>
      </div>
    </div>

    <!-- 监控概览卡片 -->
    <div class="monitor-overview">
      <div class="overview-grid">
        <!-- IP地址池监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <div class="card-title">
              <Globe class="card-icon" />
              IP地址池监控
            </div>
            <div
              class="card-status"
              :class="ipPoolStatus.status"
            >
              {{ ipPoolStatus.label }}
            </div>
          </div>
          <div class="card-content">
            <div class="metric-item">
              <span class="metric-label">总地址数</span>
              <span class="metric-value">{{ ipPoolData.total }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">已分配</span>
              <span class="metric-value">{{ ipPoolData.allocated }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">使用率</span>
              <span class="metric-value">{{ ipPoolData.usage }}%</span>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: ipPoolData.usage + '%' }"
                :class="getUsageClass(ipPoolData.usage)"
              ></div>
            </div>
          </div>
        </div>

        <!-- DHCP服务监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <div class="card-title">
              <Server class="card-icon" />
              DHCP服务监控
            </div>
            <div
              class="card-status"
              :class="dhcpStatus.status"
            >
              {{ dhcpStatus.label }}
            </div>
          </div>
          <div class="card-content">
            <div class="metric-item">
              <span class="metric-label">活动租约</span>
              <span class="metric-value">{{ dhcpData.activeLeases }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">可用地址</span>
              <span class="metric-value">{{ dhcpData.availableAddresses }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">租约成功率</span>
              <span class="metric-value">{{ dhcpData.successRate }}%</span>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill success"
                :style="{ width: dhcpData.successRate + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <!-- DNS服务监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <div class="card-title">
              <Database class="card-icon" />
              DNS服务监控
            </div>
            <div
              class="card-status"
              :class="dnsStatus.status"
            >
              {{ dnsStatus.label }}
            </div>
          </div>
          <div class="card-content">
            <div class="metric-item">
              <span class="metric-label">查询次数/分钟</span>
              <span class="metric-value">{{ dnsData.queriesPerMinute }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">解析成功率</span>
              <span class="metric-value">{{ dnsData.resolutionRate }}%</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">平均响应时间</span>
              <span class="metric-value">{{ dnsData.avgResponseTime }}ms</span>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill success"
                :style="{ width: dnsData.resolutionRate + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 网络连通性监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <div class="card-title">
              <Activity class="card-icon" />
              网络连通性监控
            </div>
            <div
              class="card-status"
              :class="connectivityStatus.status"
            >
              {{ connectivityStatus.label }}
            </div>
          </div>
          <div class="card-content">
            <div class="metric-item">
              <span class="metric-label">在线设备</span>
              <span class="metric-value">{{ connectivityData.onlineDevices }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">平均延迟</span>
              <span class="metric-value">{{ connectivityData.avgLatency }}ms</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">网络可用性</span>
              <span class="metric-value">{{ connectivityData.availability }}%</span>
            </div>
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: connectivityData.availability + '%' }"
                :class="getUsageClass(connectivityData.availability)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细监控表格 -->
    <div class="monitor-details">
      <div class="section-header">
        <h2 class="section-title">详细监控信息</h2>
        <div class="section-actions">
          <a-select
            v-model:value="selectedTimeRange"
            style="width: 120px"
          >
            <a-select-option value="1h">最近1小时</a-select-option>
            <a-select-option value="6h">最近6小时</a-select-option>
            <a-select-option value="24h">最近24小时</a-select-option>
            <a-select-option value="7d">最近7天</a-select-option>
          </a-select>
          <a-button @click="exportData">
            <Download class="btn-icon" />
            导出数据
          </a-button>
        </div>
      </div>

      <!-- 监控数据表格 -->
      <div class="monitor-table">
        <a-table
          :columns="tableColumns"
          :data-source="monitorData"
          :loading="loading"
          :pagination="{ pageSize: 10 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status }}
              </a-tag>
            </template>
            <template v-if="column.key === 'usage'">
              <div class="usage-cell">
                <span>{{ record.usage }}%</span>
                <div class="mini-progress">
                  <div
                    class="mini-progress-fill"
                    :style="{ width: record.usage + '%' }"
                    :class="getUsageClass(record.usage)"
                  ></div>
                </div>
              </div>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button
                  size="small"
                  @click="viewDetails(record)"
                >
                  <Eye class="btn-icon" />
                  详情
                </a-button>
                <a-button
                  size="small"
                  @click="configureItem(record)"
                >
                  <Settings class="btn-icon" />
                  配置
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Network, RefreshCw, Settings, Globe, Server, Database, Activity, Download, Eye } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedTimeRange = ref('24h')

// 监控数据
const ipPoolData = reactive({
  total: 1024,
  allocated: 756,
  usage: 73.8
})

const dhcpData = reactive({
  activeLeases: 234,
  availableAddresses: 790,
  successRate: 99.2
})

const dnsData = reactive({
  queriesPerMinute: 1250,
  resolutionRate: 98.7,
  avgResponseTime: 12
})

const connectivityData = reactive({
  onlineDevices: 187,
  avgLatency: 8,
  availability: 99.5
})

// 状态定义
const ipPoolStatus = reactive({ status: 'warning', label: '使用率较高' })
const dhcpStatus = reactive({ status: 'success', label: '运行正常' })
const dnsStatus = reactive({ status: 'success', label: '运行正常' })
const connectivityStatus = reactive({ status: 'success', label: '连接正常' })

// 表格配置
const tableColumns = [
  { title: '监控项目', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '使用率', dataIndex: 'usage', key: 'usage' },
  { title: '最后更新', dataIndex: 'lastUpdate', key: 'lastUpdate' },
  { title: '操作', key: 'actions' }
]

const monitorData = ref([
  {
    id: 1,
    name: '主IP地址池',
    type: 'IP Pool',
    status: '正常',
    usage: 73,
    lastUpdate: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    name: 'DHCP服务器-1',
    type: 'DHCP',
    status: '正常',
    usage: 45,
    lastUpdate: '2024-01-15 14:30:20'
  },
  {
    id: 3,
    name: 'DNS服务器-主',
    type: 'DNS',
    status: '正常',
    usage: 32,
    lastUpdate: '2024-01-15 14:30:18'
  }
])

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const showConfigModal = () => {
  console.log('显示配置模态框')
}

const exportData = () => {
  console.log('导出数据')
}

const viewDetails = (record: any) => {
  console.log('查看详情:', record)
}

const configureItem = (record: any) => {
  console.log('配置项目:', record)
}

const getUsageClass = (usage: number) => {
  if (usage >= 90) return 'danger'
  if (usage >= 70) return 'warning'
  return 'success'
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    正常: 'green',
    警告: 'orange',
    异常: 'red'
  }
  return colors[status] || 'default'
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-infrastructure-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 24px 24px 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.monitor-overview {
  margin: 0 24px 24px 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.monitor-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-status.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.card-status.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.card-status.danger {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 600;
  color: #1a1a1a;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 12px;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill.success {
  background: #52c41a;
}

.progress-fill.warning {
  background: #faad14;
}

.progress-fill.danger {
  background: #ff4d4f;
}

.monitor-details {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin: 0 24px 24px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.usage-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mini-progress {
  width: 60px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.mini-progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 18px;
  height: 18px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
