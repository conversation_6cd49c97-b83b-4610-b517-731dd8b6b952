<template>
  <div
    class="network-security-monitor"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Shield class="title-icon" />
            网络安全监控
          </h1>
          <p class="page-description">监控网络安全事件、威胁检测和流量分析</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="showSecurityReport"
        >
          <FileText class="btn-icon" />
          安全报告
        </a-button>
      </div>
    </div>

    <!-- 安全概览 -->
    <div class="security-overview">
      <div class="overview-grid">
        <div class="security-card threat-level">
          <div class="card-header">
            <div class="card-title">
              <AlertTriangle class="card-icon" />
              威胁等级
            </div>
            <div
              class="threat-indicator"
              :class="securityData.threatLevel"
            >
              {{ getThreatLevelLabel(securityData.threatLevel) }}
            </div>
          </div>
          <div class="card-content">
            <div class="threat-details">
              <span>今日检测到 {{ securityData.todayThreats }} 个威胁</span>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Eye class="card-icon" />
              实时监控
            </div>
            <div class="card-value">{{ securityData.activeConnections }}</div>
          </div>
          <div class="card-content">
            <div class="monitoring-stats">
              <div class="stat-item">
                <span class="stat-label">活动连接</span>
                <span class="stat-value">{{ securityData.activeConnections }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">可疑连接</span>
                <span class="stat-value suspicious">{{ securityData.suspiciousConnections }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Ban class="card-icon" />
              阻断统计
            </div>
            <div class="card-value">{{ securityData.blockedAttempts }}</div>
          </div>
          <div class="card-content">
            <div class="block-stats">
              <div class="stat-item">
                <span class="stat-label">今日阻断</span>
                <span class="stat-value">{{ securityData.todayBlocked }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功率</span>
                <span class="stat-value">{{ securityData.blockSuccessRate }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Activity class="card-icon" />
              流量分析
            </div>
            <div class="card-value">{{ securityData.trafficVolume }}GB</div>
          </div>
          <div class="card-content">
            <div class="traffic-stats">
              <div class="stat-item">
                <span class="stat-label">正常流量</span>
                <span class="stat-value">{{ securityData.normalTraffic }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">异常流量</span>
                <span class="stat-value anomaly">{{ securityData.anomalousTraffic }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全事件列表 -->
    <div class="security-events">
      <div class="section-header">
        <h2 class="section-title">安全事件</h2>
        <div class="section-filters">
          <a-select
            v-model:value="selectedSeverity"
            style="width: 120px"
          >
            <a-select-option value="all">全部级别</a-select-option>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedType"
            style="width: 150px"
          >
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="intrusion">入侵检测</a-select-option>
            <a-select-option value="malware">恶意软件</a-select-option>
            <a-select-option value="ddos">DDoS攻击</a-select-option>
            <a-select-option value="bruteforce">暴力破解</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="events-list">
        <div
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-item"
          :class="event.severity"
        >
          <div class="event-icon">
            <component
              :is="getEventIcon(event.type)"
              class="icon"
            />
          </div>
          <div class="event-content">
            <div class="event-header">
              <h4 class="event-title">{{ event.title }}</h4>
              <div
                class="event-severity"
                :class="event.severity"
              >
                {{ getSeverityLabel(event.severity) }}
              </div>
            </div>
            <p class="event-description">{{ event.description }}</p>
            <div class="event-details">
              <span class="event-source">来源: {{ event.sourceIP }}</span>
              <span class="event-target">目标: {{ event.targetIP }}</span>
              <span class="event-time">{{ event.timestamp }}</span>
            </div>
          </div>
          <div class="event-actions">
            <a-button
              size="small"
              @click="investigateEvent(event)"
            >
              <Search class="btn-icon" />
              调查
            </a-button>
            <a-button
              size="small"
              :disabled="event.blocked"
              @click="blockSource(event)"
            >
              <Ban class="btn-icon" />
              {{ event.blocked ? '已阻断' : '阻断' }}
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 威胁情报 -->
    <div class="threat-intelligence">
      <div class="section-header">
        <h2 class="section-title">威胁情报</h2>
        <div class="intelligence-summary">
          <span class="intel-item">恶意IP: {{ threatIntel.maliciousIPs }}</span>
          <span class="intel-item">恶意域名: {{ threatIntel.maliciousDomains }}</span>
          <span class="intel-item">已知威胁: {{ threatIntel.knownThreats }}</span>
        </div>
      </div>

      <div class="intelligence-grid">
        <div class="intel-card">
          <div class="intel-header">
            <h3>恶意IP地址</h3>
            <span class="intel-count">{{ threatIntel.maliciousIPs }}</span>
          </div>
          <div class="intel-list">
            <div
              v-for="ip in maliciousIPs"
              :key="ip.address"
              class="intel-item"
            >
              <span class="ip-address">{{ ip.address }}</span>
              <span class="ip-country">{{ ip.country }}</span>
              <span class="ip-threat-type">{{ ip.threatType }}</span>
            </div>
          </div>
        </div>

        <div class="intel-card">
          <div class="intel-header">
            <h3>攻击类型分布</h3>
          </div>
          <div class="attack-distribution">
            <div
              v-for="attack in attackTypes"
              :key="attack.type"
              class="attack-item"
            >
              <div class="attack-info">
                <span class="attack-name">{{ attack.name }}</span>
                <span class="attack-count">{{ attack.count }}</span>
              </div>
              <div class="attack-bar">
                <div
                  class="attack-fill"
                  :style="{ width: attack.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="intel-card">
          <div class="intel-header">
            <h3>地理位置分析</h3>
          </div>
          <div class="geo-analysis">
            <div
              v-for="location in geoData"
              :key="location.country"
              class="geo-item"
            >
              <span class="geo-country">{{ location.country }}</span>
              <span class="geo-attacks">{{ location.attacks }} 次攻击</span>
              <div
                class="geo-risk"
                :class="location.riskLevel"
              >
                {{ location.riskLevel }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Shield, RefreshCw, FileText, AlertTriangle, Eye, Ban, Activity, Search, Zap, Bug, Skull } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedSeverity = ref('all')
const selectedType = ref('all')

// 安全数据
const securityData = reactive({
  threatLevel: 'medium',
  todayThreats: 23,
  activeConnections: 1247,
  suspiciousConnections: 8,
  blockedAttempts: 156,
  todayBlocked: 45,
  blockSuccessRate: 98.5,
  trafficVolume: 2.4,
  normalTraffic: 94.2,
  anomalousTraffic: 5.8
})

// 安全事件数据
const securityEvents = ref([
  {
    id: 1,
    title: 'DDoS攻击检测',
    description: '检测到来自多个IP地址的大量请求，疑似DDoS攻击',
    type: 'ddos',
    severity: 'critical',
    sourceIP: '*************',
    targetIP: '********',
    timestamp: '2024-01-15 14:30:25',
    blocked: false
  },
  {
    id: 2,
    title: '暴力破解尝试',
    description: 'SSH服务检测到多次登录失败，疑似暴力破解攻击',
    type: 'bruteforce',
    severity: 'high',
    sourceIP: '************',
    targetIP: '********',
    timestamp: '2024-01-15 14:25:18',
    blocked: true
  },
  {
    id: 3,
    title: '恶意软件通信',
    description: '检测到主机与已知恶意域名的通信',
    type: 'malware',
    severity: 'medium',
    sourceIP: '********5',
    targetIP: 'malicious.example.com',
    timestamp: '2024-01-15 14:20:12',
    blocked: true
  }
])

// 威胁情报数据
const threatIntel = reactive({
  maliciousIPs: 1247,
  maliciousDomains: 856,
  knownThreats: 2103
})

const maliciousIPs = ref([
  { address: '************', country: '美国', threatType: '暴力破解' },
  { address: '*************', country: '俄罗斯', threatType: 'DDoS' },
  { address: '**********', country: '中国', threatType: '恶意软件' }
])

const attackTypes = ref([
  { type: 'ddos', name: 'DDoS攻击', count: 45, percentage: 35 },
  { type: 'bruteforce', name: '暴力破解', count: 32, percentage: 25 },
  { type: 'malware', name: '恶意软件', count: 28, percentage: 22 },
  { type: 'intrusion', name: '入侵检测', count: 23, percentage: 18 }
])

const geoData = ref([
  { country: '美国', attacks: 156, riskLevel: 'high' },
  { country: '俄罗斯', attacks: 89, riskLevel: 'critical' },
  { country: '中国', attacks: 67, riskLevel: 'medium' },
  { country: '德国', attacks: 34, riskLevel: 'low' }
])

// 过滤后的事件列表
const filteredEvents = computed(() => {
  let filtered = securityEvents.value

  if (selectedSeverity.value !== 'all') {
    filtered = filtered.filter((event) => event.severity === selectedSeverity.value)
  }

  if (selectedType.value !== 'all') {
    filtered = filtered.filter((event) => event.type === selectedType.value)
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const showSecurityReport = () => {
  console.log('显示安全报告')
}

const investigateEvent = (event: any) => {
  console.log('调查事件:', event)
}

const blockSource = (event: any) => {
  console.log('阻断来源:', event)
  event.blocked = true
}

const getThreatLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[level] || level
}

const getSeverityLabel = (severity: string) => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[severity] || severity
}

const getEventIcon = (type: string) => {
  const icons: Record<string, any> = {
    ddos: Zap,
    bruteforce: Ban,
    malware: Bug,
    intrusion: Skull
  }
  return icons[type] || AlertTriangle
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-security-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.security-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.security-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.security-card.threat-level {
  border-left: 4px solid #faad14;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
}

.threat-indicator {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.threat-indicator.low {
  background: #f6ffed;
  color: #52c41a;
}

.threat-indicator.medium {
  background: #fffbe6;
  color: #faad14;
}

.threat-indicator.high {
  background: #fff2e8;
  color: #fa8c16;
}

.threat-indicator.critical {
  background: #fff2f0;
  color: #ff4d4f;
}

.card-content {
  margin-top: 12px;
}

.threat-details {
  font-size: 14px;
  color: #666;
}

.monitoring-stats,
.block-stats,
.traffic-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.stat-value.suspicious {
  color: #ff4d4f;
}

.stat-value.anomaly {
  color: #fa8c16;
}

.security-events,
.threat-intelligence {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.event-item.critical {
  border-left: 4px solid #ff4d4f;
  background: #fff2f0;
}

.event-item.high {
  border-left: 4px solid #fa8c16;
  background: #fff2e8;
}

.event-item.medium {
  border-left: 4px solid #faad14;
  background: #fffbe6;
}

.event-item.low {
  border-left: 4px solid #52c41a;
  background: #f6ffed;
}

.event-icon {
  margin-top: 2px;
  color: #666;
}

.event-content {
  flex: 1;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.event-severity {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.event-severity.critical {
  background: #ff4d4f;
  color: white;
}

.event-severity.high {
  background: #fa8c16;
  color: white;
}

.event-severity.medium {
  background: #faad14;
  color: white;
}

.event-severity.low {
  background: #52c41a;
  color: white;
}

.event-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.event-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.event-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 2px;
}

.intelligence-summary {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #666;
}

.intel-item {
  font-weight: 500;
}

.intelligence-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.intel-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.intel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.intel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.intel-count {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.intel-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.intel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.ip-address {
  font-family: monospace;
  font-weight: 600;
  color: #1a1a1a;
}

.ip-country {
  color: #666;
}

.ip-threat-type {
  color: #ff4d4f;
  font-weight: 500;
}

.attack-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attack-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attack-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.attack-name {
  color: #1a1a1a;
  font-weight: 500;
}

.attack-count {
  color: #666;
}

.attack-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.attack-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.geo-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.geo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.geo-country {
  font-weight: 600;
  color: #1a1a1a;
}

.geo-attacks {
  color: #666;
}

.geo-risk {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.geo-risk.low {
  background: #f6ffed;
  color: #52c41a;
}

.geo-risk.medium {
  background: #fffbe6;
  color: #faad14;
}

.geo-risk.high {
  background: #fff2e8;
  color: #fa8c16;
}

.geo-risk.critical {
  background: #fff2f0;
  color: #ff4d4f;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 16px;
  height: 16px;
}

.icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
