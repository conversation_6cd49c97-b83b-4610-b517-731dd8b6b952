<template>
  <div class="application-layer-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Globe class="title-icon" />
            应用层监控
          </h1>
          <p class="page-description">监控Web服务、邮件服务、数据库连接等应用层服务</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="addService"
        >
          <Plus class="btn-icon" />
          添加服务
        </a-button>
      </div>
    </div>

    <!-- 服务概览 -->
    <div class="services-overview">
      <div class="overview-grid">
        <div class="service-card web">
          <div class="card-header">
            <div class="card-icon">
              <Globe class="icon" />
            </div>
            <div class="card-info">
              <h3>Web服务</h3>
              <div class="service-count">{{ serviceStats.web.total }}</div>
            </div>
          </div>
          <div class="card-metrics">
            <div class="metric">
              <span class="metric-label">在线</span>
              <span class="metric-value online">{{ serviceStats.web.online }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">离线</span>
              <span class="metric-value offline">{{ serviceStats.web.offline }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">平均响应</span>
              <span class="metric-value">{{ serviceStats.web.avgResponse }}ms</span>
            </div>
          </div>
        </div>

        <div class="service-card mail">
          <div class="card-header">
            <div class="card-icon">
              <Mail class="icon" />
            </div>
            <div class="card-info">
              <h3>邮件服务</h3>
              <div class="service-count">{{ serviceStats.mail.total }}</div>
            </div>
          </div>
          <div class="card-metrics">
            <div class="metric">
              <span class="metric-label">在线</span>
              <span class="metric-value online">{{ serviceStats.mail.online }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">离线</span>
              <span class="metric-value offline">{{ serviceStats.mail.offline }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">队列长度</span>
              <span class="metric-value">{{ serviceStats.mail.queueLength }}</span>
            </div>
          </div>
        </div>

        <div class="service-card database">
          <div class="card-header">
            <div class="card-icon">
              <Database class="icon" />
            </div>
            <div class="card-info">
              <h3>数据库服务</h3>
              <div class="service-count">{{ serviceStats.database.total }}</div>
            </div>
          </div>
          <div class="card-metrics">
            <div class="metric">
              <span class="metric-label">在线</span>
              <span class="metric-value online">{{ serviceStats.database.online }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">离线</span>
              <span class="metric-value offline">{{ serviceStats.database.offline }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">连接数</span>
              <span class="metric-value">{{ serviceStats.database.connections }}</span>
            </div>
          </div>
        </div>

        <div class="service-card api">
          <div class="card-header">
            <div class="card-icon">
              <Code class="icon" />
            </div>
            <div class="card-info">
              <h3>API服务</h3>
              <div class="service-count">{{ serviceStats.api.total }}</div>
            </div>
          </div>
          <div class="card-metrics">
            <div class="metric">
              <span class="metric-label">在线</span>
              <span class="metric-value online">{{ serviceStats.api.online }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">离线</span>
              <span class="metric-value offline">{{ serviceStats.api.offline }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">请求/分钟</span>
              <span class="metric-value">{{ serviceStats.api.requestsPerMinute }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务监控列表 -->
    <div class="services-monitoring">
      <div class="section-header">
        <h2 class="section-title">服务监控</h2>
        <div class="section-filters">
          <a-select
            v-model:value="selectedServiceType"
            style="width: 120px"
          >
            <a-select-option value="all">全部服务</a-select-option>
            <a-select-option value="web">Web服务</a-select-option>
            <a-select-option value="mail">邮件服务</a-select-option>
            <a-select-option value="database">数据库</a-select-option>
            <a-select-option value="api">API服务</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedStatus"
            style="width: 100px"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="warning">警告</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="services-grid">
        <div
          v-for="service in filteredServices"
          :key="service.id"
          class="service-monitor-card"
          :class="service.status"
        >
          <div class="service-header">
            <div class="service-info">
              <div class="service-icon">
                <component
                  :is="getServiceIcon(service.type)"
                  class="icon"
                />
              </div>
              <div class="service-details">
                <h3 class="service-name">{{ service.name }}</h3>
                <p class="service-url">{{ service.url }}</p>
              </div>
            </div>
            <div
              class="service-status"
              :class="service.status"
            >
              <div class="status-dot"></div>
              <span>{{ getStatusLabel(service.status) }}</span>
            </div>
          </div>

          <div class="service-metrics">
            <div class="metrics-grid">
              <div class="metric-item">
                <span class="metric-label">响应时间</span>
                <span
                  class="metric-value"
                  :class="getResponseTimeClass(service.responseTime)"
                >
                  {{ service.responseTime }}ms
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">可用性</span>
                <span class="metric-value">{{ service.uptime }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">错误率</span>
                <span
                  class="metric-value"
                  :class="getErrorRateClass(service.errorRate)"
                >
                  {{ service.errorRate }}%
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">最后检查</span>
                <span class="metric-value">{{ service.lastCheck }}</span>
              </div>
            </div>
          </div>

          <div class="service-chart">
            <div class="chart-header">
              <span>24小时响应时间趋势</span>
            </div>
            <div class="mini-chart">
              <div class="chart-placeholder">
                <TrendingUp class="chart-icon" />
                <span>响应时间图表</span>
              </div>
            </div>
          </div>

          <div class="service-actions">
            <a-button
              size="small"
              @click="testService(service)"
            >
              <Play class="btn-icon" />
              测试
            </a-button>
            <a-button
              size="small"
              @click="viewServiceDetails(service)"
            >
              <Eye class="btn-icon" />
              详情
            </a-button>
            <a-button
              size="small"
              @click="configureService(service)"
            >
              <Settings class="btn-icon" />
              配置
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- SSL证书监控 -->
    <div class="ssl-monitoring">
      <div class="section-header">
        <h2 class="section-title">SSL证书监控</h2>
        <div class="ssl-summary">
          <span class="ssl-item valid">{{ sslStats.valid }} 有效</span>
          <span class="ssl-item expiring">{{ sslStats.expiring }} 即将过期</span>
          <span class="ssl-item expired">{{ sslStats.expired }} 已过期</span>
        </div>
      </div>

      <div class="ssl-certificates">
        <div
          v-for="cert in sslCertificates"
          :key="cert.id"
          class="ssl-cert-card"
          :class="cert.status"
        >
          <div class="cert-header">
            <div class="cert-info">
              <h4 class="cert-domain">{{ cert.domain }}</h4>
              <p class="cert-issuer">颁发者: {{ cert.issuer }}</p>
            </div>
            <div
              class="cert-status"
              :class="cert.status"
            >
              {{ getCertStatusLabel(cert.status) }}
            </div>
          </div>
          <div class="cert-details">
            <div class="cert-dates">
              <span class="cert-date">颁发日期: {{ cert.issuedDate }}</span>
              <span class="cert-date">过期日期: {{ cert.expiryDate }}</span>
            </div>
            <div
              class="cert-days-left"
              :class="cert.status"
            >
              {{ cert.daysLeft }} 天后过期
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Globe, RefreshCw, Plus, Mail, Database, Code, TrendingUp, Play, Eye, Settings } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedServiceType = ref('all')
const selectedStatus = ref('all')

// 服务统计数据
const serviceStats = reactive({
  web: { total: 12, online: 11, offline: 1, avgResponse: 245 },
  mail: { total: 4, online: 4, offline: 0, queueLength: 23 },
  database: { total: 6, online: 5, offline: 1, connections: 156 },
  api: { total: 8, online: 7, offline: 1, requestsPerMinute: 1247 }
})

// 服务监控数据
const services = ref([
  {
    id: 1,
    name: '主网站',
    url: 'https://www.example.com',
    type: 'web',
    status: 'online',
    responseTime: 245,
    uptime: 99.8,
    errorRate: 0.2,
    lastCheck: '2分钟前'
  },
  {
    id: 2,
    name: 'API网关',
    url: 'https://api.example.com',
    type: 'api',
    status: 'online',
    responseTime: 156,
    uptime: 99.9,
    errorRate: 0.1,
    lastCheck: '1分钟前'
  },
  {
    id: 3,
    name: '邮件服务器',
    url: 'mail.example.com:587',
    type: 'mail',
    status: 'warning',
    responseTime: 1250,
    uptime: 98.5,
    errorRate: 1.5,
    lastCheck: '3分钟前'
  },
  {
    id: 4,
    name: '主数据库',
    url: 'db.example.com:5432',
    type: 'database',
    status: 'online',
    responseTime: 45,
    uptime: 99.7,
    errorRate: 0.3,
    lastCheck: '1分钟前'
  },
  {
    id: 5,
    name: '文档站点',
    url: 'https://docs.example.com',
    type: 'web',
    status: 'offline',
    responseTime: 0,
    uptime: 95.2,
    errorRate: 100,
    lastCheck: '5分钟前'
  }
])

// SSL证书数据
const sslStats = reactive({
  valid: 8,
  expiring: 2,
  expired: 1
})

const sslCertificates = ref([
  {
    id: 1,
    domain: 'www.example.com',
    issuer: "Let's Encrypt",
    issuedDate: '2024-01-01',
    expiryDate: '2024-04-01',
    daysLeft: 75,
    status: 'valid'
  },
  {
    id: 2,
    domain: 'api.example.com',
    issuer: 'DigiCert',
    issuedDate: '2023-12-01',
    expiryDate: '2024-02-15',
    daysLeft: 15,
    status: 'expiring'
  },
  {
    id: 3,
    domain: 'old.example.com',
    issuer: "Let's Encrypt",
    issuedDate: '2023-10-01',
    expiryDate: '2024-01-01',
    daysLeft: -14,
    status: 'expired'
  }
])

// 过滤后的服务列表
const filteredServices = computed(() => {
  let filtered = services.value

  if (selectedServiceType.value !== 'all') {
    filtered = filtered.filter((service) => service.type === selectedServiceType.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((service) => service.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const addService = () => {
  console.log('添加服务')
}

const testService = (service: any) => {
  console.log('测试服务:', service)
}

const viewServiceDetails = (service: any) => {
  console.log('查看服务详情:', service)
}

const configureService = (service: any) => {
  console.log('配置服务:', service)
}

const getServiceIcon = (type: string) => {
  const icons: Record<string, any> = {
    web: Globe,
    mail: Mail,
    database: Database,
    api: Code
  }
  return icons[type] || Globe
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return labels[status] || status
}

const getResponseTimeClass = (responseTime: number) => {
  if (responseTime === 0) return 'offline'
  if (responseTime > 1000) return 'slow'
  if (responseTime > 500) return 'medium'
  return 'fast'
}

const getErrorRateClass = (errorRate: number) => {
  if (errorRate >= 10) return 'high'
  if (errorRate >= 1) return 'medium'
  return 'low'
}

const getCertStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    valid: '有效',
    expiring: '即将过期',
    expired: '已过期'
  }
  return labels[status] || status
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.application-layer-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.services-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.service-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.service-card.web {
  border-left: 4px solid #1890ff;
}

.service-card.mail {
  border-left: 4px solid #52c41a;
}

.service-card.database {
  border-left: 4px solid #fa8c16;
}

.service-card.api {
  border-left: 4px solid #722ed1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.card-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.service-count {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.metric-value.online {
  color: #52c41a;
}

.metric-value.offline {
  color: #ff4d4f;
}

.services-monitoring,
.ssl-monitoring {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.service-monitor-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.service-monitor-card.online {
  border-left: 4px solid #52c41a;
}

.service-monitor-card.offline {
  border-left: 4px solid #ff4d4f;
}

.service-monitor-card.warning {
  border-left: 4px solid #faad14;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.service-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.service-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.service-status.online .status-dot {
  background: #52c41a;
}

.service-status.offline .status-dot {
  background: #ff4d4f;
}

.service-status.warning .status-dot {
  background: #faad14;
}

.service-metrics {
  margin-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.metric-item .metric-label {
  color: #666;
}

.metric-item .metric-value {
  font-weight: 600;
  color: #1a1a1a;
}

.metric-item .metric-value.fast {
  color: #52c41a;
}

.metric-item .metric-value.medium {
  color: #faad14;
}

.metric-item .metric-value.slow {
  color: #ff4d4f;
}

.metric-item .metric-value.offline {
  color: #999;
}

.metric-item .metric-value.low {
  color: #52c41a;
}

.metric-item .metric-value.high {
  color: #ff4d4f;
}

.service-chart {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
}

.chart-header {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 40px;
  color: #999;
  font-size: 12px;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.ssl-summary {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.ssl-item {
  font-weight: 600;
}

.ssl-item.valid {
  color: #52c41a;
}

.ssl-item.expiring {
  color: #faad14;
}

.ssl-item.expired {
  color: #ff4d4f;
}

.ssl-certificates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.ssl-cert-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.ssl-cert-card.valid {
  border-left: 4px solid #52c41a;
}

.ssl-cert-card.expiring {
  border-left: 4px solid #faad14;
}

.ssl-cert-card.expired {
  border-left: 4px solid #ff4d4f;
}

.cert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.cert-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.cert-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.cert-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.cert-status.valid {
  background: #f6ffed;
  color: #52c41a;
}

.cert-status.expiring {
  background: #fffbe6;
  color: #faad14;
}

.cert-status.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

.cert-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cert-dates {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.cert-days-left {
  font-size: 14px;
  font-weight: 600;
}

.cert-days-left.valid {
  color: #52c41a;
}

.cert-days-left.expiring {
  color: #faad14;
}

.cert-days-left.expired {
  color: #ff4d4f;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.icon {
  width: 16px;
  height: 16px;
}

.chart-icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
