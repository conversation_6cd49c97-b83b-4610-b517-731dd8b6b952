<template>
  <div
    class="network-data-analytics"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Monitor class="title-icon" />
            网络数据分析
          </h1>
          <p class="page-description">网络容量规划、趋势分析和性能优化建议</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="generateReport">
          <FileText class="btn-icon" />
          生成报告
        </a-button>
        <a-button
          type="primary"
          @click="showPredictionModal"
        >
          <TrendingUp class="btn-icon" />
          容量预测
        </a-button>
      </div>
    </div>

    <!-- 分析概览 -->
    <div class="analytics-overview">
      <div class="overview-grid">
        <div class="analytics-card">
          <div class="card-header">
            <div class="card-title">
              <BarChart3 class="card-icon" />
              网络利用率趋势
            </div>
            <div
              class="trend-indicator"
              :class="utilizationTrend.direction"
            >
              {{ utilizationTrend.change }}%
            </div>
          </div>
          <div class="card-content">
            <div class="current-utilization">
              <span class="utilization-value">{{ currentUtilization }}%</span>
              <span class="utilization-label">当前利用率</span>
            </div>
            <div class="utilization-chart">
              <!-- 这里可以集成图表组件 -->
              <div class="chart-placeholder">
                <LineChart class="chart-icon" />
                <span>7天趋势图</span>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <div class="card-header">
            <div class="card-title">
              <Clock class="card-icon" />
              容量预测
            </div>
            <div class="prediction-period">未来6个月</div>
          </div>
          <div class="card-content">
            <div class="capacity-prediction">
              <div class="prediction-item">
                <span class="prediction-label">预计达到80%</span>
                <span class="prediction-value">{{ capacityPrediction.reach80 }}</span>
              </div>
              <div class="prediction-item">
                <span class="prediction-label">预计达到90%</span>
                <span class="prediction-value">{{ capacityPrediction.reach90 }}</span>
              </div>
              <div class="prediction-item">
                <span class="prediction-label">建议扩容时间</span>
                <span class="prediction-value warning">{{ capacityPrediction.expandTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <div class="card-header">
            <div class="card-title">
              <AlertTriangle class="card-icon" />
              性能瓶颈分析
            </div>
            <div class="bottleneck-count">{{ performanceBottlenecks.length }} 个</div>
          </div>
          <div class="card-content">
            <div class="bottleneck-list">
              <div
                v-for="bottleneck in performanceBottlenecks.slice(0, 3)"
                :key="bottleneck.id"
                class="bottleneck-item"
                :class="bottleneck.severity"
              >
                <span class="bottleneck-name">{{ bottleneck.name }}</span>
                <span class="bottleneck-impact">{{ bottleneck.impact }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <div class="card-header">
            <div class="card-title">
              <Target class="card-icon" />
              优化建议
            </div>
            <div class="suggestions-count">{{ optimizationSuggestions.length }} 条</div>
          </div>
          <div class="card-content">
            <div class="suggestions-preview">
              <div
                v-for="suggestion in optimizationSuggestions.slice(0, 2)"
                :key="suggestion.id"
                class="suggestion-item"
                :class="suggestion.priority"
              >
                <span class="suggestion-text">{{ suggestion.title }}</span>
                <span class="suggestion-impact">{{ suggestion.expectedImprovement }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细分析图表 -->
    <div class="detailed-analytics">
      <div class="analytics-tabs">
        <a-tabs
          v-model:active-key="activeTab"
          type="card"
        >
          <a-tab-pane
            key="traffic"
            tab="流量分析"
          >
            <div class="traffic-analysis">
              <div class="analysis-controls">
                <a-radio-group
                  v-model:value="trafficTimeRange"
                  button-style="solid"
                >
                  <a-radio-button value="24h">24小时</a-radio-button>
                  <a-radio-button value="7d">7天</a-radio-button>
                  <a-radio-button value="30d">30天</a-radio-button>
                </a-radio-group>
              </div>
              <div class="charts-grid">
                <div class="chart-container">
                  <h3>带宽使用趋势</h3>
                  <div class="chart-placeholder large">
                    <BarChart3 class="chart-icon" />
                    <span>带宽使用趋势图表</span>
                  </div>
                </div>
                <div class="chart-container">
                  <h3>流量分布</h3>
                  <div class="chart-placeholder large">
                    <PieChart class="chart-icon" />
                    <span>流量分布饼图</span>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane
            key="performance"
            tab="性能分析"
          >
            <div class="performance-analysis">
              <div class="performance-metrics">
                <div class="metric-card">
                  <h4>平均延迟</h4>
                  <div class="metric-value">{{ performanceMetrics.avgLatency }}ms</div>
                  <div
                    class="metric-trend"
                    :class="performanceMetrics.latencyTrend"
                  >
                    {{ performanceMetrics.latencyChange }}%
                  </div>
                </div>
                <div class="metric-card">
                  <h4>吞吐量</h4>
                  <div class="metric-value">{{ performanceMetrics.throughput }}Mbps</div>
                  <div
                    class="metric-trend"
                    :class="performanceMetrics.throughputTrend"
                  >
                    {{ performanceMetrics.throughputChange }}%
                  </div>
                </div>
                <div class="metric-card">
                  <h4>丢包率</h4>
                  <div class="metric-value">{{ performanceMetrics.packetLoss }}%</div>
                  <div
                    class="metric-trend"
                    :class="performanceMetrics.packetLossTrend"
                  >
                    {{ performanceMetrics.packetLossChange }}%
                  </div>
                </div>
                <div class="metric-card">
                  <h4>连接数</h4>
                  <div class="metric-value">{{ performanceMetrics.connections }}</div>
                  <div
                    class="metric-trend"
                    :class="performanceMetrics.connectionsTrend"
                  >
                    {{ performanceMetrics.connectionsChange }}%
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane
            key="capacity"
            tab="容量规划"
          >
            <div class="capacity-planning">
              <div class="planning-summary">
                <div class="summary-card">
                  <h4>当前容量状态</h4>
                  <div class="capacity-status">
                    <div class="capacity-bar">
                      <div
                        class="capacity-fill"
                        :style="{ width: capacityStatus.usage + '%' }"
                        :class="getCapacityClass(capacityStatus.usage)"
                      ></div>
                    </div>
                    <div class="capacity-details">
                      <span>已使用: {{ capacityStatus.used }}GB</span>
                      <span>总容量: {{ capacityStatus.total }}GB</span>
                      <span>使用率: {{ capacityStatus.usage }}%</span>
                    </div>
                  </div>
                </div>
                <div class="summary-card">
                  <h4>增长预测</h4>
                  <div class="growth-prediction">
                    <div class="growth-item">
                      <span class="growth-period">3个月</span>
                      <span class="growth-value">{{ growthPrediction.threeMonths }}%</span>
                    </div>
                    <div class="growth-item">
                      <span class="growth-period">6个月</span>
                      <span class="growth-value">{{ growthPrediction.sixMonths }}%</span>
                    </div>
                    <div class="growth-item">
                      <span class="growth-period">12个月</span>
                      <span class="growth-value">{{ growthPrediction.twelveMonths }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane
            key="optimization"
            tab="优化建议"
          >
            <div class="optimization-suggestions">
              <div class="suggestions-list">
                <div
                  v-for="suggestion in optimizationSuggestions"
                  :key="suggestion.id"
                  class="suggestion-card"
                  :class="suggestion.priority"
                >
                  <div class="suggestion-header">
                    <h4 class="suggestion-title">{{ suggestion.title }}</h4>
                    <div
                      class="suggestion-priority"
                      :class="suggestion.priority"
                    >
                      {{ getPriorityLabel(suggestion.priority) }}
                    </div>
                  </div>
                  <p class="suggestion-description">{{ suggestion.description }}</p>
                  <div class="suggestion-metrics">
                    <div class="suggestion-metric">
                      <span class="metric-label">预期改善</span>
                      <span class="metric-value">{{ suggestion.expectedImprovement }}</span>
                    </div>
                    <div class="suggestion-metric">
                      <span class="metric-label">实施难度</span>
                      <span class="metric-value">{{ suggestion.difficulty }}</span>
                    </div>
                    <div class="suggestion-metric">
                      <span class="metric-label">预计成本</span>
                      <span class="metric-value">{{ suggestion.estimatedCost }}</span>
                    </div>
                  </div>
                  <div class="suggestion-actions">
                    <a-button
                      size="small"
                      @click="viewSuggestionDetails(suggestion)"
                    >
                      查看详情
                    </a-button>
                    <a-button
                      size="small"
                      type="primary"
                      @click="implementSuggestion(suggestion)"
                    >
                      开始实施
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Monitor, RefreshCw, FileText, TrendingUp, BarChart3, Clock, AlertTriangle, Target, LineChart, PieChart } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const activeTab = ref('traffic')
const trafficTimeRange = ref('24h')

// 分析数据
const currentUtilization = ref(68)
const utilizationTrend = reactive({
  direction: 'up',
  change: 12
})

const capacityPrediction = reactive({
  reach80: '3个月后',
  reach90: '5个月后',
  expandTime: '4个月内'
})

const performanceBottlenecks = ref([
  { id: 1, name: 'Core-Switch-01', severity: 'high', impact: 25 },
  { id: 2, name: 'WAN链路', severity: 'medium', impact: 18 },
  { id: 3, name: 'DB服务器', severity: 'low', impact: 12 }
])

const optimizationSuggestions = ref([
  {
    id: 1,
    title: '升级核心交换机带宽',
    description: '当前核心交换机带宽利用率过高，建议升级到更高带宽的设备',
    priority: 'high',
    expectedImprovement: '30%性能提升',
    difficulty: '中等',
    estimatedCost: '¥50,000'
  },
  {
    id: 2,
    title: '优化网络拓扑结构',
    description: '重新设计网络拓扑，减少网络跳数，提高传输效率',
    priority: 'medium',
    expectedImprovement: '15%延迟降低',
    difficulty: '较高',
    estimatedCost: '¥30,000'
  },
  {
    id: 3,
    title: '实施QoS策略',
    description: '配置服务质量策略，优先保障关键业务流量',
    priority: 'medium',
    expectedImprovement: '20%关键业务性能提升',
    difficulty: '较低',
    estimatedCost: '¥5,000'
  }
])

const performanceMetrics = reactive({
  avgLatency: 12,
  latencyTrend: 'down',
  latencyChange: -8,
  throughput: 850,
  throughputTrend: 'up',
  throughputChange: 15,
  packetLoss: 0.2,
  packetLossTrend: 'down',
  packetLossChange: -0.1,
  connections: 1247,
  connectionsTrend: 'up',
  connectionsChange: 23
})

const capacityStatus = reactive({
  used: 680,
  total: 1000,
  usage: 68
})

const growthPrediction = reactive({
  threeMonths: 78,
  sixMonths: 85,
  twelveMonths: 95
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const generateReport = () => {
  console.log('生成分析报告')
}

const showPredictionModal = () => {
  console.log('显示容量预测模态框')
}

const viewSuggestionDetails = (suggestion: any) => {
  console.log('查看建议详情:', suggestion)
}

const implementSuggestion = (suggestion: any) => {
  console.log('实施建议:', suggestion)
}

const getPriorityLabel = (priority: string) => {
  const labels: Record<string, string> = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return labels[priority] || priority
}

const getCapacityClass = (usage: number) => {
  if (usage >= 90) return 'critical'
  if (usage >= 70) return 'warning'
  return 'normal'
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-data-analytics {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.analytics-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.analytics-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 600;
}

.trend-indicator {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.trend-indicator.up {
  background: #f6ffed;
  color: #52c41a;
}

.trend-indicator.down {
  background: #fff2f0;
  color: #ff4d4f;
}

.current-utilization {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.utilization-value {
  font-size: 32px;
  font-weight: 600;
  color: #1a1a1a;
}

.utilization-label {
  font-size: 14px;
  color: #666;
}

.utilization-chart {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  font-size: 12px;
}

.chart-placeholder.large {
  height: 200px;
  justify-content: center;
  font-size: 14px;
}

.prediction-period {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.capacity-prediction {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prediction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.prediction-label {
  color: #666;
}

.prediction-value {
  font-weight: 600;
  color: #1a1a1a;
}

.prediction-value.warning {
  color: #faad14;
}

.bottleneck-count,
.suggestions-count {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.bottleneck-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bottleneck-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.bottleneck-item.high {
  background: #fff2f0;
  color: #ff4d4f;
}

.bottleneck-item.medium {
  background: #fffbe6;
  color: #faad14;
}

.bottleneck-item.low {
  background: #f6ffed;
  color: #52c41a;
}

.bottleneck-name {
  font-weight: 500;
}

.bottleneck-impact {
  font-weight: 600;
}

.suggestions-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}

.suggestion-item.high {
  background: #fff2f0;
  border-left: 3px solid #ff4d4f;
}

.suggestion-item.medium {
  background: #fffbe6;
  border-left: 3px solid #faad14;
}

.suggestion-text {
  flex: 1;
  color: #1a1a1a;
  font-weight: 500;
}

.suggestion-impact {
  color: #666;
  font-weight: 600;
}

.detailed-analytics {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.analysis-controls {
  margin-bottom: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-container {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.chart-container h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.metric-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
}

.metric-trend.up {
  color: #52c41a;
}

.metric-trend.down {
  color: #ff4d4f;
}

.planning-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.summary-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.summary-card h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.capacity-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.capacity-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.capacity-fill.normal {
  background: #52c41a;
}

.capacity-fill.warning {
  background: #faad14;
}

.capacity-fill.critical {
  background: #ff4d4f;
}

.capacity-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.growth-prediction {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.growth-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.growth-period {
  color: #666;
}

.growth-value {
  font-weight: 600;
  color: #1a1a1a;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.suggestion-card.high {
  border-left: 4px solid #ff4d4f;
}

.suggestion-card.medium {
  border-left: 4px solid #faad14;
}

.suggestion-card.low {
  border-left: 4px solid #52c41a;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.suggestion-title {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.suggestion-priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.suggestion-priority.high {
  background: #ff4d4f;
  color: white;
}

.suggestion-priority.medium {
  background: #faad14;
  color: white;
}

.suggestion-priority.low {
  background: #52c41a;
  color: white;
}

.suggestion-description {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.suggestion-metrics {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.suggestion-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suggestion-metric .metric-label {
  font-size: 12px;
  color: #666;
}

.suggestion-metric .metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 16px;
  height: 16px;
}

.chart-icon {
  width: 32px;
  height: 32px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
