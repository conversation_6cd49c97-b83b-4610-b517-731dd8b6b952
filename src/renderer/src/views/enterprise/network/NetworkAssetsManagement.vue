<template>
  <div class="network-assets-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Server class="title-icon" />
            网络资产管理
          </h1>
          <p class="page-description">管理网络设备清单、配置备份和生命周期跟踪</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="exportAssets">
          <Download class="btn-icon" />
          导出清单
        </a-button>
        <a-button
          type="primary"
          @click="addAsset"
        >
          <Plus class="btn-icon" />
          添加资产
        </a-button>
      </div>
    </div>

    <!-- 资产概览 -->
    <div class="assets-overview">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-header">
            <h3>总资产数量</h3>
            <div class="card-value">{{ assetStats.total }}</div>
          </div>
          <div class="card-breakdown">
            <div class="breakdown-item">
              <span class="breakdown-label">在线</span>
              <span class="breakdown-value online">{{ assetStats.online }}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">离线</span>
              <span class="breakdown-value offline">{{ assetStats.offline }}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">维护中</span>
              <span class="breakdown-value maintenance">{{ assetStats.maintenance }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>设备类型分布</h3>
          </div>
          <div class="type-distribution">
            <div
              v-for="type in deviceTypes"
              :key="type.name"
              class="type-item"
            >
              <div class="type-info">
                <span class="type-name">{{ type.name }}</span>
                <span class="type-count">{{ type.count }}</span>
              </div>
              <div class="type-bar">
                <div
                  class="type-fill"
                  :style="{ width: type.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>生命周期状态</h3>
          </div>
          <div class="lifecycle-status">
            <div class="lifecycle-item new">
              <span class="lifecycle-label">新设备</span>
              <span class="lifecycle-count">{{ lifecycleStats.new }}</span>
            </div>
            <div class="lifecycle-item active">
              <span class="lifecycle-label">使用中</span>
              <span class="lifecycle-count">{{ lifecycleStats.active }}</span>
            </div>
            <div class="lifecycle-item aging">
              <span class="lifecycle-label">老化</span>
              <span class="lifecycle-count">{{ lifecycleStats.aging }}</span>
            </div>
            <div class="lifecycle-item eol">
              <span class="lifecycle-label">报废</span>
              <span class="lifecycle-count">{{ lifecycleStats.eol }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>配置备份状态</h3>
          </div>
          <div class="backup-status">
            <div class="backup-metric">
              <span class="metric-label">已备份设备</span>
              <span class="metric-value">{{ backupStats.backedUp }}/{{ assetStats.total }}</span>
            </div>
            <div class="backup-metric">
              <span class="metric-label">最近备份</span>
              <span class="metric-value">{{ backupStats.lastBackup }}</span>
            </div>
            <div class="backup-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: backupStats.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ backupStats.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="assets-list">
      <div class="list-header">
        <h2 class="section-title">网络资产清单</h2>
        <div class="list-filters">
          <a-select
            v-model:value="selectedType"
            style="width: 120px"
          >
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedStatus"
            style="width: 100px"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索设备名称、IP或序列号"
            style="width: 250px"
            @search="filterAssets"
          />
        </div>
      </div>

      <div class="assets-table">
        <a-table
          :columns="tableColumns"
          :data-source="filteredAssets"
          :loading="loading"
          :pagination="{ pageSize: 15 }"
          row-key="id"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusLabel(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'type'">
              <div class="type-cell">
                <component
                  :is="getDeviceIcon(record.type)"
                  class="type-icon"
                />
                <span>{{ getTypeLabel(record.type) }}</span>
              </div>
            </template>
            <template v-if="column.key === 'lifecycle'">
              <a-tag :color="getLifecycleColor(record.lifecycle)">
                {{ getLifecycleLabel(record.lifecycle) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'backup'">
              <div class="backup-cell">
                <div
                  class="backup-indicator"
                  :class="record.hasBackup ? 'backed-up' : 'no-backup'"
                >
                  <CheckCircle
                    v-if="record.hasBackup"
                    class="backup-icon"
                  />
                  <XCircle
                    v-else
                    class="backup-icon"
                  />
                </div>
                <span>{{ record.hasBackup ? '已备份' : '未备份' }}</span>
              </div>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button
                  size="small"
                  @click="viewAssetDetails(record)"
                >
                  <Eye class="btn-icon" />
                  详情
                </a-button>
                <a-button
                  size="small"
                  :disabled="!record.canBackup"
                  @click="backupConfig(record)"
                >
                  <Save class="btn-icon" />
                  备份
                </a-button>
                <a-button
                  size="small"
                  @click="editAsset(record)"
                >
                  <Edit class="btn-icon" />
                  编辑
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Server,
  RefreshCw,
  Download,
  Plus,
  Eye,
  Save,
  Edit,
  CheckCircle,
  XCircle,
  Network,
  Router,
  Shield,
  Wifi,
  HardDrive
} from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')
const searchText = ref('')

// 资产统计数据
const assetStats = reactive({
  total: 156,
  online: 142,
  offline: 8,
  maintenance: 6
})

// 设备类型分布
const deviceTypes = ref([
  { name: '交换机', count: 45, percentage: 29 },
  { name: '服务器', count: 38, percentage: 24 },
  { name: '无线AP', count: 32, percentage: 21 },
  { name: '路由器', count: 25, percentage: 16 },
  { name: '防火墙', count: 16, percentage: 10 }
])

// 生命周期统计
const lifecycleStats = reactive({
  new: 12,
  active: 128,
  aging: 14,
  eol: 2
})

// 备份统计
const backupStats = reactive({
  backedUp: 134,
  lastBackup: '2小时前',
  percentage: 86
})

// 表格列配置
const tableColumns = [
  { title: '设备名称', dataIndex: 'name', key: 'name', width: 150, fixed: 'left' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: 'IP地址', dataIndex: 'ip', key: 'ip', width: 130 },
  { title: '序列号', dataIndex: 'serialNumber', key: 'serialNumber', width: 150 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '生命周期', dataIndex: 'lifecycle', key: 'lifecycle', width: 100 },
  { title: '位置', dataIndex: 'location', key: 'location', width: 120 },
  { title: '配置备份', dataIndex: 'backup', key: 'backup', width: 120 },
  { title: '最后更新', dataIndex: 'lastUpdate', key: 'lastUpdate', width: 150 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' }
]

// 资产数据
const assets = ref([
  {
    id: 1,
    name: 'Core-Switch-01',
    type: 'switch',
    ip: '***********0',
    serialNumber: 'SW001234567',
    status: 'online',
    lifecycle: 'active',
    location: '机房A-机柜01',
    hasBackup: true,
    canBackup: true,
    lastUpdate: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    name: 'Web-Server-01',
    type: 'server',
    ip: '*************',
    serialNumber: 'SV001234567',
    status: 'online',
    lifecycle: 'active',
    location: '机房A-机柜03',
    hasBackup: true,
    canBackup: true,
    lastUpdate: '2024-01-15 14:25:18'
  },
  {
    id: 3,
    name: 'Edge-Router-01',
    type: 'router',
    ip: '***********',
    serialNumber: 'RT001234567',
    status: 'online',
    lifecycle: 'aging',
    location: '机房A-机柜01',
    hasBackup: false,
    canBackup: true,
    lastUpdate: '2024-01-15 14:20:12'
  },
  {
    id: 4,
    name: 'Firewall-Main',
    type: 'firewall',
    ip: '*************',
    serialNumber: 'FW001234567',
    status: 'maintenance',
    lifecycle: 'active',
    location: '机房A-机柜02',
    hasBackup: true,
    canBackup: false,
    lastUpdate: '2024-01-15 13:45:30'
  },
  {
    id: 5,
    name: 'AP-Floor1-01',
    type: 'ap',
    ip: '*************',
    serialNumber: 'AP001234567',
    status: 'offline',
    lifecycle: 'active',
    location: '1楼大厅',
    hasBackup: false,
    canBackup: false,
    lastUpdate: '2024-01-15 12:30:45'
  }
])

// 过滤后的资产列表
const filteredAssets = computed(() => {
  let filtered = assets.value

  if (selectedType.value !== 'all') {
    filtered = filtered.filter((asset) => asset.type === selectedType.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((asset) => asset.status === selectedStatus.value)
  }

  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(
      (asset) => asset.name.toLowerCase().includes(search) || asset.ip.includes(search) || asset.serialNumber.toLowerCase().includes(search)
    )
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const exportAssets = () => {
  console.log('导出资产清单')
}

const addAsset = () => {
  console.log('添加资产')
}

const filterAssets = () => {
  // 过滤逻辑已在computed中处理
}

const viewAssetDetails = (asset: any) => {
  console.log('查看资产详情:', asset)
}

const backupConfig = (asset: any) => {
  console.log('备份配置:', asset)
}

const editAsset = (asset: any) => {
  console.log('编辑资产:', asset)
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    online: 'green',
    offline: 'red',
    maintenance: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中'
  }
  return labels[status] || status
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    ap: '无线AP',
    server: '服务器'
  }
  return labels[type] || type
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    switch: Network,
    router: Router,
    firewall: Shield,
    ap: Wifi,
    server: HardDrive
  }
  return icons[type] || Server
}

const getLifecycleColor = (lifecycle: string) => {
  const colors: Record<string, string> = {
    new: 'blue',
    active: 'green',
    aging: 'orange',
    eol: 'red'
  }
  return colors[lifecycle] || 'default'
}

const getLifecycleLabel = (lifecycle: string) => {
  const labels: Record<string, string> = {
    new: '新设备',
    active: '使用中',
    aging: '老化',
    eol: '报废'
  }
  return labels[lifecycle] || lifecycle
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-assets-management {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.assets-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 16px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 32px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-breakdown {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.breakdown-item {
  text-align: center;
}

.breakdown-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.breakdown-value {
  font-size: 16px;
  font-weight: 600;
}

.breakdown-value.online {
  color: #52c41a;
}

.breakdown-value.offline {
  color: #ff4d4f;
}

.breakdown-value.maintenance {
  color: #faad14;
}

.type-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.type-name {
  color: #1a1a1a;
  font-weight: 500;
}

.type-count {
  color: #666;
}

.type-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.type-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.lifecycle-status {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.lifecycle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.lifecycle-item.new {
  background: #e6f7ff;
  color: #1890ff;
}

.lifecycle-item.active {
  background: #f6ffed;
  color: #52c41a;
}

.lifecycle-item.aging {
  background: #fffbe6;
  color: #faad14;
}

.lifecycle-item.eol {
  background: #fff2f0;
  color: #ff4d4f;
}

.lifecycle-label {
  font-weight: 500;
}

.lifecycle-count {
  font-weight: 600;
}

.backup-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 600;
  color: #1a1a1a;
}

.backup-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #52c41a;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.assets-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.list-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.type-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  width: 16px;
  height: 16px;
  color: #666;
}

.backup-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-indicator {
  display: flex;
  align-items: center;
}

.backup-indicator.backed-up {
  color: #52c41a;
}

.backup-indicator.no-backup {
  color: #ff4d4f;
}

.backup-icon {
  width: 16px;
  height: 16px;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
