<template>
  <div class="wifi-network-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Wifi class="title-icon" />
            无线网络监控
          </h1>
          <p class="page-description">监控WiFi覆盖、AP设备状态和无线信号质量</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="showCoverageMap"
        >
          <Map class="btn-icon" />
          覆盖地图
        </a-button>
      </div>
    </div>

    <!-- WiFi概览 -->
    <div class="wifi-overview">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-header">
            <h3>接入点总数</h3>
            <div class="card-value">{{ wifiStats.totalAPs }}</div>
          </div>
          <div class="card-footer">
            <span class="status-item online">{{ wifiStats.onlineAPs }} 在线</span>
            <span class="status-item offline">{{ wifiStats.offlineAPs }} 离线</span>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>连接设备数</h3>
            <div class="card-value">{{ wifiStats.connectedDevices }}</div>
          </div>
          <div class="card-footer">
            <span class="trend-indicator up">+{{ wifiStats.deviceGrowth }}%</span>
            <span class="trend-label">较昨日</span>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>平均信号强度</h3>
            <div class="card-value">{{ wifiStats.avgSignalStrength }}dBm</div>
          </div>
          <div class="card-footer">
            <div
              class="signal-quality"
              :class="getSignalQualityClass(wifiStats.avgSignalStrength)"
            >
              {{ getSignalQualityLabel(wifiStats.avgSignalStrength) }}
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>网络利用率</h3>
            <div class="card-value">{{ wifiStats.networkUtilization }}%</div>
          </div>
          <div class="card-footer">
            <div class="utilization-bar">
              <div
                class="utilization-fill"
                :style="{ width: wifiStats.networkUtilization + '%' }"
                :class="getUtilizationClass(wifiStats.networkUtilization)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AP设备列表 -->
    <div class="ap-devices">
      <div class="section-header">
        <h2 class="section-title">接入点设备</h2>
        <div class="section-filters">
          <a-select
            v-model:value="selectedFloor"
            style="width: 120px"
          >
            <a-select-option value="all">全部楼层</a-select-option>
            <a-select-option value="1">1楼</a-select-option>
            <a-select-option value="2">2楼</a-select-option>
            <a-select-option value="3">3楼</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedStatus"
            style="width: 100px"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="warning">警告</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="ap-grid">
        <div
          v-for="ap in filteredAPs"
          :key="ap.id"
          class="ap-card"
          :class="ap.status"
        >
          <div class="ap-header">
            <div class="ap-info">
              <h3 class="ap-name">{{ ap.name }}</h3>
              <p class="ap-location">{{ ap.location }}</p>
            </div>
            <div
              class="ap-status"
              :class="ap.status"
            >
              <div class="status-dot"></div>
              <span>{{ getStatusLabel(ap.status) }}</span>
            </div>
          </div>

          <div class="ap-metrics">
            <div class="metric-row">
              <div class="metric">
                <span class="metric-label">连接设备</span>
                <span class="metric-value">{{ ap.connectedDevices }}</span>
              </div>
              <div class="metric">
                <span class="metric-label">信号强度</span>
                <span class="metric-value">{{ ap.signalStrength }}dBm</span>
              </div>
            </div>
            <div class="metric-row">
              <div class="metric">
                <span class="metric-label">频道</span>
                <span class="metric-value">{{ ap.channel }}</span>
              </div>
              <div class="metric">
                <span class="metric-label">带宽使用</span>
                <span class="metric-value">{{ ap.bandwidthUsage }}%</span>
              </div>
            </div>
          </div>

          <div class="ap-chart">
            <div class="chart-header">
              <span>设备连接趋势</span>
            </div>
            <div class="mini-chart">
              <!-- 这里可以集成图表组件 -->
              <div class="chart-placeholder">
                <Activity class="chart-icon" />
                <span>24小时趋势</span>
              </div>
            </div>
          </div>

          <div class="ap-actions">
            <a-button
              size="small"
              @click="configureAP(ap)"
            >
              <Settings class="btn-icon" />
              配置
            </a-button>
            <a-button
              size="small"
              @click="viewAPDetails(ap)"
            >
              <Eye class="btn-icon" />
              详情
            </a-button>
            <a-button
              size="small"
              :disabled="ap.status === 'offline'"
              @click="restartAP(ap)"
            >
              <RotateCw class="btn-icon" />
              重启
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 频道使用分析 -->
    <div class="channel-analysis">
      <div class="section-header">
        <h2 class="section-title">频道使用分析</h2>
        <div class="frequency-tabs">
          <a-radio-group
            v-model:value="selectedFrequency"
            button-style="solid"
          >
            <a-radio-button value="2.4GHz">2.4GHz</a-radio-button>
            <a-radio-button value="5GHz">5GHz</a-radio-button>
          </a-radio-group>
        </div>
      </div>

      <div class="channel-grid">
        <div
          v-for="channel in channelData"
          :key="channel.number"
          class="channel-card"
          :class="getChannelUsageClass(channel.usage)"
        >
          <div class="channel-number">{{ channel.number }}</div>
          <div class="channel-usage">{{ channel.usage }}%</div>
          <div class="channel-aps">{{ channel.apCount }} APs</div>
          <div class="usage-bar">
            <div
              class="usage-fill"
              :style="{ width: channel.usage + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Wifi, RefreshCw, Map, Activity, Settings, Eye, RotateCw } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedFloor = ref('all')
const selectedStatus = ref('all')
const selectedFrequency = ref('2.4GHz')

// WiFi统计数据
const wifiStats = reactive({
  totalAPs: 24,
  onlineAPs: 22,
  offlineAPs: 2,
  connectedDevices: 156,
  deviceGrowth: 12,
  avgSignalStrength: -45,
  networkUtilization: 68
})

// AP设备数据
const apDevices = ref([
  {
    id: 1,
    name: 'AP-Floor1-01',
    location: '1楼大厅',
    status: 'online',
    connectedDevices: 24,
    signalStrength: -42,
    channel: 6,
    bandwidthUsage: 45,
    floor: 1
  },
  {
    id: 2,
    name: 'AP-Floor1-02',
    location: '1楼会议室',
    status: 'online',
    connectedDevices: 12,
    signalStrength: -38,
    channel: 11,
    bandwidthUsage: 32,
    floor: 1
  },
  {
    id: 3,
    name: 'AP-Floor2-01',
    location: '2楼办公区',
    status: 'warning',
    connectedDevices: 18,
    signalStrength: -55,
    channel: 1,
    bandwidthUsage: 78,
    floor: 2
  },
  {
    id: 4,
    name: 'AP-Floor2-02',
    location: '2楼休息区',
    status: 'offline',
    connectedDevices: 0,
    signalStrength: 0,
    channel: 0,
    bandwidthUsage: 0,
    floor: 2
  }
])

// 频道数据
const channelData = ref([
  { number: 1, usage: 25, apCount: 3 },
  { number: 6, usage: 45, apCount: 5 },
  { number: 11, usage: 78, apCount: 8 },
  { number: 36, usage: 32, apCount: 4 },
  { number: 40, usage: 56, apCount: 6 },
  { number: 44, usage: 23, apCount: 2 }
])

// 过滤后的AP列表
const filteredAPs = computed(() => {
  let filtered = apDevices.value

  if (selectedFloor.value !== 'all') {
    filtered = filtered.filter((ap) => ap.floor.toString() === selectedFloor.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((ap) => ap.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const showCoverageMap = () => {
  console.log('显示覆盖地图')
}

const configureAP = (ap: any) => {
  console.log('配置AP:', ap)
}

const viewAPDetails = (ap: any) => {
  console.log('查看AP详情:', ap)
}

const restartAP = (ap: any) => {
  console.log('重启AP:', ap)
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return labels[status] || status
}

const getSignalQualityClass = (strength: number) => {
  if (strength >= -30) return 'excellent'
  if (strength >= -50) return 'good'
  if (strength >= -70) return 'fair'
  return 'poor'
}

const getSignalQualityLabel = (strength: number) => {
  if (strength >= -30) return '优秀'
  if (strength >= -50) return '良好'
  if (strength >= -70) return '一般'
  return '较差'
}

const getUtilizationClass = (usage: number) => {
  if (usage >= 80) return 'high'
  if (usage >= 60) return 'medium'
  return 'low'
}

const getChannelUsageClass = (usage: number) => {
  if (usage >= 70) return 'high-usage'
  if (usage >= 40) return 'medium-usage'
  return 'low-usage'
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wifi-network-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.wifi-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.card-footer {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.status-item.online {
  color: #52c41a;
}

.status-item.offline {
  color: #ff4d4f;
}

.trend-indicator.up {
  color: #52c41a;
  font-weight: 600;
}

.trend-label {
  color: #666;
}

.signal-quality {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.signal-quality.excellent {
  background: #f6ffed;
  color: #52c41a;
}

.signal-quality.good {
  background: #fff7e6;
  color: #fa8c16;
}

.signal-quality.fair {
  background: #fffbe6;
  color: #faad14;
}

.signal-quality.poor {
  background: #fff2f0;
  color: #ff4d4f;
}

.utilization-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.utilization-fill.low {
  background: #52c41a;
}

.utilization-fill.medium {
  background: #faad14;
}

.utilization-fill.high {
  background: #ff4d4f;
}

.ap-devices,
.channel-analysis {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.ap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.ap-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.ap-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ap-card.online {
  border-left: 4px solid #52c41a;
}

.ap-card.offline {
  border-left: 4px solid #ff4d4f;
}

.ap-card.warning {
  border-left: 4px solid #faad14;
}

.ap-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.ap-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.ap-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.ap-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.ap-status.online .status-dot {
  background: #52c41a;
}

.ap-status.offline .status-dot {
  background: #ff4d4f;
}

.ap-status.warning .status-dot {
  background: #faad14;
}

.ap-metrics {
  margin-bottom: 16px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.ap-chart {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.chart-header {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 40px;
  color: #999;
  font-size: 12px;
}

.ap-actions {
  display: flex;
  gap: 8px;
}

.channel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.channel-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 2px solid transparent;
}

.channel-card.low-usage {
  border-color: #52c41a;
}

.channel-card.medium-usage {
  border-color: #faad14;
}

.channel-card.high-usage {
  border-color: #ff4d4f;
}

.channel-number {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.channel-usage {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.channel-aps {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.usage-bar {
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.chart-icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
