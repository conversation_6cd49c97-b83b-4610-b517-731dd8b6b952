<!--
  文件：RoomRealtimeMonitor.vue
  功能：机房实时监控（占位页面）
  依赖：vue, vue-router
  作者：AI 助手
  修改时间：自动生成
-->
<template>
  <div class="page-wrap">
    <header class="page-header">
      <h1>机房实时监控</h1>
      <p class="sub">本页面为占位，后续将接入实时监控看板。</p>
      <button
        class="btn"
        @click="goBack"
        >返回机房总览</button
      >
    </header>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()

/**
 * 返回机房管理总览
 */
const goBack = () => {
  router.push({ name: 'RoomManagement' })
}
</script>

<style scoped>
.page-wrap {
  padding: 24px;
}
.page-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.sub {
  color: #64748b;
}
.btn {
  padding: 8px 12px;
  border-radius: 8px;
  background: #2563eb;
  color: #fff;
  border: none;
  cursor: pointer;
}
.btn:hover {
  background: #1d4ed8;
}
</style>
